<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            // Add consultation timing columns if they don't exist
            if (!Schema::hasColumn('appointments', 'consultation_started_at')) {
                $table->timestamp('consultation_started_at')->nullable()->after('video_session_id');
            }
            if (!Schema::hasColumn('appointments', 'consultation_ended_at')) {
                $table->timestamp('consultation_ended_at')->nullable()->after('consultation_started_at');
            }

            // Add audio recording columns if they don't exist
            if (!Schema::hasColumn('appointments', 'has_audio_recording')) {
                $table->boolean('has_audio_recording')->default(false)->after('consultation_ended_at');
            }
            if (!Schema::hasColumn('appointments', 'audio_recording_path')) {
                $table->string('audio_recording_path')->nullable()->after('has_audio_recording');
            }
            if (!Schema::hasColumn('appointments', 'recording_started_at')) {
                $table->timestamp('recording_started_at')->nullable()->after('audio_recording_path');
            }
            if (!Schema::hasColumn('appointments', 'recording_ended_at')) {
                $table->timestamp('recording_ended_at')->nullable()->after('recording_started_at');
            }
            if (!Schema::hasColumn('appointments', 'transcription')) {
                $table->text('transcription')->nullable()->after('recording_ended_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropColumn([
                'consultation_started_at',
                'consultation_ended_at',
                'has_audio_recording',
                'audio_recording_path',
                'recording_started_at',
                'recording_ended_at',
                'transcription'
            ]);
        });
    }
};
