# Notification Troubleshooting Guide

## 🔍 **Common Issues and Solutions**

### **Issue 1: No FCM Token Generated**
**Symptoms:** Debug screen shows FCM token failed
**Solutions:**
- Check internet connection
- Verify `google-services.json` is properly configured
- Ensure Firebase project ID matches in both Flutter and backend
- Check Android permissions in `AndroidManifest.xml`

### **Issue 2: Token Registration Failed**
**Symptoms:** Debug screen shows "Token Registration: ❌ Failed"
**Solutions:**
- Verify user is logged in
- Check backend API endpoint: `/api/notifications/device-token`
- Verify authentication token is valid
- Check backend logs for API errors

### **Issue 3: Backend Can't Send Notifications**
**Symptoms:** Backend test shows Firebase connection failed
**Solutions:**
- Verify `FIREBASE_API_KEY` in backend `.env` file
- Ensure it matches the Flutter app's Firebase project
- Test Firebase connection: `/api/notifications/test-firebase`
- Check backend logs for FCM API errors

### **Issue 4: Notifications Sent But Not Received**
**Symptoms:** Backend logs show success, but phone doesn't ring
**Solutions:**
- Check Android notification permissions
- Verify notification channels are created
- Test with foreground and background scenarios
- Check Do Not Disturb settings on device
- Verify ringtone file exists in `android/app/src/main/res/raw/`

### **Issue 5: Notifications Received But No Ringtone**
**Symptoms:** Notification appears but no sound
**Solutions:**
- Check notification channel configuration
- Verify `call_ringtone.mp3` file exists
- Check device volume settings
- Test notification channel settings in Android settings

## 🧪 **Testing Checklist**

### **Flutter App Testing:**
- [ ] Firebase initialized successfully
- [ ] FCM token generated (check length > 100 chars)
- [ ] Device info captured (platform, model, user agent)
- [ ] Token registration API call successful
- [ ] Notification permissions granted

### **Backend Testing:**
- [ ] Device tokens stored in database
- [ ] Firebase API key configured correctly
- [ ] NotificationService can retrieve user tokens
- [ ] FCM HTTP API returns success
- [ ] Video call notification contains proper data

### **End-to-End Testing:**
- [ ] Patient logged into Flutter app
- [ ] Provider can access appointment interface
- [ ] "Start Video Call" button triggers notification
- [ ] Patient phone rings with custom sound
- [ ] Full-screen incoming call appears
- [ ] Accept/decline buttons work
- [ ] Call connects properly

## 🔧 **Debug Commands**

### **Check FCM Token:**
```dart
final messaging = FirebaseMessaging.instance;
final token = await messaging.getToken();
print('FCM Token: $token');
```

### **Test Local Notification:**
```dart
await notificationService.showVideoCallNotification(mockMessage);
```

### **Check Device Tokens in Backend:**
```sql
SELECT * FROM device_tokens WHERE user_id = [PATIENT_USER_ID];
```

### **Test Firebase API Manually:**
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_FIREBASE_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "DEVICE_TOKEN",
    "notification": {
      "title": "Test Call",
      "body": "Testing notification"
    },
    "data": {
      "type": "video_call"
    }
  }'
```

## 📞 **Expected Notification Data Structure**

When provider starts video call, this data should be sent:
```json
{
  "type": "video_call",
  "appointment_id": "123",
  "session_id": "vs_uuid",
  "channel_name": "medroid_123_timestamp",
  "provider_name": "Dr. Provider Name",
  "provider_id": "1",
  "call_action": "incoming_call",
  "timestamp": "2025-01-20T10:30:00Z"
}
```

## 🚨 **If All Else Fails**

1. **Check Laravel logs:** `storage/logs/laravel.log`
2. **Check Firebase project console** for delivery reports
3. **Test with a simple test notification** from Firebase console
4. **Verify user has proper role permissions** (patient role)
5. **Check appointment status** (must be confirmed/paid)