import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:medroid_app/services/device_info_service.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/utils/constants.dart';

/// Debug helper to analyze notification flow
class NotificationDebugHelper {
  static Future<Map<String, dynamic>> analyzeNotificationFlow() async {
    final results = <String, dynamic>{};
    
    try {
      debugPrint('🔍 Starting notification flow analysis...');
      
      // 1. Check Firebase initialization
      results['firebase_initialized'] = await _checkFirebaseInitialization();
      
      // 2. Get FCM token
      results['fcm_token'] = await _getFCMToken();
      
      // 3. Get device info and user agent
      results['device_info'] = await _getDeviceInfo();
      
      // 4. Test device token registration
      results['token_registration'] = await _testTokenRegistration();
      
      // 5. Check notification permissions
      results['permissions'] = await _checkNotificationPermissions();
      
      debugPrint('✅ Notification flow analysis complete');
      debugPrint('📊 Results: $results');
      
      return results;
      
    } catch (e) {
      debugPrint('❌ Error during notification flow analysis: $e');
      results['error'] = e.toString();
      return results;
    }
  }
  
  static Future<bool> _checkFirebaseInitialization() async {
    try {
      final messaging = FirebaseMessaging.instance;
      await messaging.getToken();
      debugPrint('✅ Firebase initialized successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Firebase initialization failed: $e');
      return false;
    }
  }
  
  static Future<Map<String, dynamic>> _getFCMToken() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final token = await messaging.getToken();
      
      if (token != null) {
        debugPrint('✅ FCM token generated successfully');
        debugPrint('🔑 Token length: ${token.length}');
        debugPrint('🔑 Token preview: ${token.substring(0, 20)}...');
        
        return {
          'success': true,
          'token_length': token.length,
          'token_preview': '${token.substring(0, 20)}...',
          'full_token': token, // For backend testing
        };
      } else {
        debugPrint('❌ FCM token is null');
        return {'success': false, 'error': 'Token is null'};
      }
    } catch (e) {
      debugPrint('❌ Error getting FCM token: $e');
      return {'success': false, 'error': e.toString()};
    }
  }
  
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final deviceInfo = await DeviceInfoService.getDeviceInfo();
      final userAgent = await DeviceInfoService.getUserAgent();
      
      debugPrint('✅ Device info collected successfully');
      debugPrint('📱 Platform: ${deviceInfo['platform']}');
      debugPrint('📱 Device type: ${deviceInfo['device_type']}');
      debugPrint('📱 User agent: $userAgent');
      
      return {
        'success': true,
        'device_info': deviceInfo,
        'user_agent': userAgent,
        'platform': deviceInfo['platform'],
        'device_type': deviceInfo['device_type'],
      };
    } catch (e) {
      debugPrint('❌ Error getting device info: $e');
      return {'success': false, 'error': e.toString()};
    }
  }
  
  static Future<Map<String, dynamic>> _testTokenRegistration() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final token = await messaging.getToken();
      
      if (token == null) {
        return {'success': false, 'error': 'No FCM token available'};
      }
      
      final deviceInfo = await DeviceInfoService.getDeviceInfo();
      final userAgent = await DeviceInfoService.getUserAgent();
      
      final payload = {
        'token': token,
        'device_type': DeviceInfoService.getNotificationDeviceType(),
        'user_agent': userAgent,
        'browser': deviceInfo['browser'] ?? 'Unknown',
        'platform': deviceInfo['platform'] ?? 'Unknown',
        'device_model': deviceInfo['device_model'] ?? 'Unknown',
        'device_brand': deviceInfo['device_brand'] ?? 'Unknown',
        'os_version': deviceInfo['os_version'] ?? 'Unknown',
        'app_version': deviceInfo['app_version'] ?? 'Unknown',
      };
      
      debugPrint('🚀 Testing device token registration...');
      debugPrint('📤 Payload: $payload');
      
      final apiService = ApiService();
      final response = await apiService.post(
        Constants.deviceTokenEndpoint,
        payload,
      );
      
      debugPrint('✅ Device token registration test completed');
      debugPrint('📥 Response: $response');
      
      return {
        'success': true,
        'payload': payload,
        'response': response,
        'endpoint': Constants.deviceTokenEndpoint,
      };
      
    } catch (e) {
      debugPrint('❌ Error testing token registration: $e');
      return {'success': false, 'error': e.toString()};
    }
  }
  
  static Future<Map<String, dynamic>> _checkNotificationPermissions() async {
    try {
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );
      
      debugPrint('✅ Notification permissions checked');
      debugPrint('🔔 Authorization status: ${settings.authorizationStatus}');
      
      return {
        'success': true,
        'authorization_status': settings.authorizationStatus.toString(),
        'alert': settings.alert.toString(),
        'badge': settings.badge.toString(),
        'sound': settings.sound.toString(),
      };
      
    } catch (e) {
      debugPrint('❌ Error checking notification permissions: $e');
      return {'success': false, 'error': e.toString()};
    }
  }
  
  /// Generate a summary report
  static String generateReport(Map<String, dynamic> results) {
    final buffer = StringBuffer();
    buffer.writeln('📋 NOTIFICATION FLOW ANALYSIS REPORT');
    buffer.writeln('=' * 50);
    
    // Firebase initialization
    buffer.writeln('🔥 Firebase: ${results['firebase_initialized'] == true ? '✅ OK' : '❌ FAILED'}');
    
    // FCM token
    final fcmToken = results['fcm_token'] as Map<String, dynamic>?;
    if (fcmToken?['success'] == true) {
      buffer.writeln('🔑 FCM Token: ✅ Generated (${fcmToken!['token_length']} chars)');
    } else {
      buffer.writeln('🔑 FCM Token: ❌ Failed - ${fcmToken?['error']}');
    }
    
    // Device info
    final deviceInfo = results['device_info'] as Map<String, dynamic>?;
    if (deviceInfo?['success'] == true) {
      buffer.writeln('📱 Device Info: ✅ Collected');
      buffer.writeln('   Platform: ${deviceInfo!['platform']}');
      buffer.writeln('   Type: ${deviceInfo['device_type']}');
      buffer.writeln('   User Agent: ${deviceInfo['user_agent']}');
    } else {
      buffer.writeln('📱 Device Info: ❌ Failed - ${deviceInfo?['error']}');
    }
    
    // Token registration
    final tokenReg = results['token_registration'] as Map<String, dynamic>?;
    if (tokenReg?['success'] == true) {
      buffer.writeln('📤 Token Registration: ✅ Successful');
      buffer.writeln('   Endpoint: ${tokenReg!['endpoint']}');
    } else {
      buffer.writeln('📤 Token Registration: ❌ Failed - ${tokenReg?['error']}');
    }
    
    // Permissions
    final permissions = results['permissions'] as Map<String, dynamic>?;
    if (permissions?['success'] == true) {
      buffer.writeln('🔔 Permissions: ✅ ${permissions!['authorization_status']}');
    } else {
      buffer.writeln('🔔 Permissions: ❌ Failed - ${permissions?['error']}');
    }
    
    buffer.writeln('=' * 50);
    
    return buffer.toString();
  }
}