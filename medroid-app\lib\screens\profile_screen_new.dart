import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:medroid_app/models/appointment.dart';
import 'package:medroid_app/screens/change_password_screen.dart';
import 'package:medroid_app/screens/provider_dashboard/provider_profile_edit_screen.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/notification_service.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/constants.dart';
import 'package:medroid_app/widgets/appointment_card.dart';
import 'package:medroid_app/widgets/appointment_preferences_form.dart';
import 'package:medroid_app/widgets/platform_aware_network_image.dart';
import 'package:medroid_app/widgets/profile_option_tile.dart';
import 'package:medroid_app/widgets/profile_section_header.dart';
import 'package:medroid_app/debug/notification_debug_screen.dart';

class ProfileScreenNew extends StatefulWidget {
  const ProfileScreenNew({Key? key}) : super(key: key);

  @override
  State<ProfileScreenNew> createState() => _ProfileScreenNewState();
}

class _ProfileScreenNewState extends State<ProfileScreenNew>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Map<String, dynamic>? _userData;
  bool _isLoading = true;
  bool _isEditing = false;
  bool _isUploadingImage = false;
  List<Appointment> _appointments = [];
  String _filter = 'upcoming'; // 'upcoming', 'past', 'all'

  final TextEditingController _nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  // Image picker variables
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadUserData();
    _loadAppointments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData({bool forceRefresh = false}) async {
    try {
      final authService = RepositoryProvider.of<AuthService>(context);
      final userData =
          await authService.getCurrentUser(forceRefresh: forceRefresh);

      if (userData != null && mounted) {
        setState(() {
          _userData = userData;
          _nameController.text = userData['name'] ?? '';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadAppointments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final response = await apiService.get(Constants.userAppointmentsEndpoint);

      final List<Appointment> appointments = [];
      if (response != null) {
        for (final appointmentData in response) {
          appointments.add(Appointment.fromJson(appointmentData));
        }
      }

      setState(() {
        _appointments = appointments;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading appointments: $e')),
        );
      }
    }
  }

  List<Appointment> get _filteredAppointments {
    final now = DateTime.now();

    switch (_filter) {
      case 'upcoming':
        // Show only confirmed (paid) appointments that are upcoming
        return _appointments.where((appointment) {
          final isUpcoming = appointment.date.isAfter(now) ||
              (appointment.date.day == now.day &&
                  appointment.date.month == now.month &&
                  appointment.date.year == now.year);
          final isConfirmed = _isAppointmentConfirmed(appointment);
          return isUpcoming && isConfirmed;
        }).toList();
      case 'pending':
        // Show appointments that need payment or are not yet confirmed
        return _appointments.where((appointment) {
          final isUpcoming = appointment.date.isAfter(now) ||
              (appointment.date.day == now.day &&
                  appointment.date.month == now.month &&
                  appointment.date.year == now.year);
          final isNotConfirmed = !_isAppointmentConfirmed(appointment);
          final isNotCancelled = !_isAppointmentCancelled(appointment);
          return isUpcoming && isNotConfirmed && isNotCancelled;
        }).toList();
      case 'cancelled':
        // Show cancelled appointments
        return _appointments
            .where((appointment) => _isAppointmentCancelled(appointment))
            .toList();
      case 'past':
        return _appointments.where((appointment) {
          return appointment.date.isBefore(now) &&
              !(appointment.date.day == now.day &&
                  appointment.date.month == now.month &&
                  appointment.date.year == now.year);
        }).toList();
      case 'all':
      default:
        return _appointments;
    }
  }

  /// Determines if an appointment is confirmed (paid)
  bool _isAppointmentConfirmed(Appointment appointment) {
    // Check payment status first
    if (appointment.paymentStatus != null) {
      return appointment.paymentStatus == 'paid' ||
          appointment.paymentStatus == 'completed';
    }

    // Fallback to appointment status
    return appointment.status != 'pending_payment' &&
        appointment.status != 'cancelled' &&
        appointment.status != 'pending';
  }

  /// Determines if an appointment is cancelled
  bool _isAppointmentCancelled(Appointment appointment) {
    return appointment.status == 'cancelled';
  }

  Future<void> _logout() async {
    final authService = RepositoryProvider.of<AuthService>(context);
    await authService.logout();

    // Navigate to login screen
    if (mounted) {
      Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
    }
  }

  Future<void> _pickImage() async {
    final XFile? image = await _picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
      maxWidth: 1024,
      maxHeight: 1024,
    );

    if (image != null) {
      setState(() {
        _isUploadingImage = true;
      });

      try {
        if (kIsWeb) {
          final bytes = await image.readAsBytes();
          final compressedBytes = await _compressImageBytes(bytes);
          await _uploadProfileImage(
              webImageBytes: compressedBytes, fileName: image.name);
        } else {
          final compressedFile = await _compressImageFile(File(image.path));
          await _uploadProfileImage(imageFile: compressedFile);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error uploading image: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isUploadingImage = false;
          });
        }
      }
    }
  }

  Future<Uint8List> _compressImageBytes(Uint8List bytes) async {
    try {
      final image = img.decodeImage(bytes);
      if (image == null) return bytes;

      final resized = img.copyResize(image, width: 512, height: 512);
      final compressed = img.encodeJpg(resized, quality: 85);
      return Uint8List.fromList(compressed);
    } catch (e) {
      debugPrint('Error compressing image bytes: $e');
      return bytes;
    }
  }

  Future<File> _compressImageFile(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final compressedBytes = await _compressImageBytes(bytes);

      final tempDir = Directory.systemTemp;
      final tempFile = File(
          '${tempDir.path}/compressed_profile_${DateTime.now().millisecondsSinceEpoch}.jpg');
      await tempFile.writeAsBytes(compressedBytes);

      return tempFile;
    } catch (e) {
      debugPrint('Error compressing image file: $e');
      return file;
    }
  }

  Future<void> _uploadProfileImage(
      {File? imageFile, Uint8List? webImageBytes, String? fileName}) async {
    try {
      final apiService = RepositoryProvider.of<ApiService>(context);

      Map<String, dynamic> result;

      if (kIsWeb && webImageBytes != null) {
        result = await apiService.uploadProfileImageWeb(
          webImageBytes,
          fileName ?? 'profile.jpg',
        );
      } else if (imageFile != null) {
        result = await apiService.uploadProfileImage(imageFile);
      } else {
        throw Exception('No image provided');
      }

      if (result['success']) {
        if (result['data'] != null && mounted) {
          final authService = RepositoryProvider.of<AuthService>(context);
          await authService.saveUserData(result['data']);
        }

        if (mounted) {
          await _loadUserData(forceRefresh: true);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Profile image updated successfully')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Failed to update profile image: ${result['message']}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error uploading profile image: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= 1024;
    final isMobile = MediaQuery.of(context).size.width < 768;

    if (isMobile) {
      // Mobile layout with tabs
      return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          title:
              const Text('My Account', style: TextStyle(color: Colors.black87)),
        ),
        body: _isLoading
            ? const Center(
                child: CircularProgressIndicator(color: AppColors.tealSurge),
              )
            : _userData == null
                ? const Center(
                    child: Text('Failed to load profile'),
                  )
                : Column(
                    children: [
                      TabBar(
                        controller: _tabController,
                        labelColor: AppColors.tealSurge,
                        unselectedLabelColor: Colors.grey,
                        indicatorColor: AppColors.tealSurge,
                        tabs: const [
                          Tab(text: 'Appointments'),
                          Tab(text: 'Profile'),
                        ],
                      ),
                      Expanded(
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            _buildAppointmentsTab(),
                            _buildMobileProfileTab(),
                          ],
                        ),
                      ),
                    ],
                  ),
      );
    }

    // Desktop/Tablet layout with new design
    return Scaffold(
      backgroundColor:
          const Color(0xFFE0F2F1), // Light teal/mint green background
      body: SafeArea(
        child: _isLoading
            ? const Center(
                child: CircularProgressIndicator(
                  color: Colors.teal,
                ),
              )
            : _userData == null
                ? const Center(
                    child: Text('Failed to load profile'),
                  )
                : Column(
                    children: [
                      // Header section
                      _buildHeader(),
                      // Content section
                      Expanded(
                        child: Container(
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(24),
                              topRight: Radius.circular(24),
                            ),
                          ),
                          child: _buildDesktopContent(),
                        ),
                      ),
                    ],
                  ),
      ),
    );
  }

  Widget _buildHeader() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth >= 1024;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;

    // Responsive padding and font sizes
    final horizontalPadding = isDesktop
        ? 32.0
        : isTablet
            ? 24.0
            : 16.0;
    final verticalPadding = isDesktop
        ? 24.0
        : isTablet
            ? 20.0
            : 16.0;
    final nameFontSize = isDesktop
        ? 28.0
        : isTablet
            ? 26.0
            : 24.0;
    final buttonPadding = isDesktop
        ? 24.0
        : isTablet
            ? 20.0
            : 16.0;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: verticalPadding,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Name with dropdown arrow
          Flexible(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Text(
                    _userData?['name']?.split(' ').first ?? 'User',
                    style: TextStyle(
                      fontSize: nameFontSize,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                const Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.black87,
                  size: 24,
                ),
              ],
            ),
          ),
          // Right side buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Edit button
              GestureDetector(
                onTap: () {
                  // For desktop/tablet, show edit mode directly
                  setState(() {
                    _isEditing = !_isEditing;
                  });
                },
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: buttonPadding,
                    vertical: isDesktop
                        ? 12
                        : isTablet
                            ? 10
                            : 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    _isEditing ? 'Done' : 'Edit',
                    style: TextStyle(
                      fontSize: isDesktop
                          ? 18
                          : isTablet
                              ? 17
                              : 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ),
              SizedBox(width: isDesktop ? 16 : 12),
              // Menu button
              Icon(
                Icons.menu,
                color: Colors.black87,
                size: isDesktop
                    ? 28
                    : isTablet
                        ? 26
                        : 24,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Show edit screen for mobile
  void _showEditScreen() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // Tab bar
              TabBar(
                controller: _tabController,
                labelColor: AppColors.tealSurge,
                unselectedLabelColor: Colors.grey,
                indicatorColor: AppColors.tealSurge,
                tabs: const [
                  Tab(text: 'Appointments'),
                  Tab(text: 'Profile'),
                ],
              ),
              // Tab content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAppointmentsTab(),
                    _buildProfileSettings(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopContent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth >= 1024;
    final maxWidth = isDesktop ? 800.0 : 600.0;

    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: _isEditing ? _buildProfileSettings() : _buildProfileDisplay(),
      ),
    );
  }

  Widget _buildMobileProfileTab() {
    return _buildProfileSettings();
  }

  Widget _buildProfileDisplay() {
    final fullName = _userData?['name'] ?? 'User Name';
    final username = '@${_userData?['email']?.split('@').first ?? 'username'}';

    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth >= 1024;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;

    // Responsive values
    final padding = isDesktop
        ? 48.0
        : isTablet
            ? 36.0
            : 24.0;
    final topSpacing = isDesktop
        ? 40.0
        : isTablet
            ? 30.0
            : 20.0;
    final imageSize = isDesktop
        ? 160.0
        : isTablet
            ? 140.0
            : 120.0;
    final nameSpacing = isDesktop
        ? 24.0
        : isTablet
            ? 20.0
            : 16.0;
    final nameFontSize = isDesktop
        ? 32.0
        : isTablet
            ? 28.0
            : 24.0;
    final usernameFontSize = isDesktop
        ? 20.0
        : isTablet
            ? 18.0
            : 16.0;
    final statsSpacing = isDesktop
        ? 48.0
        : isTablet
            ? 40.0
            : 32.0;
    final iconSize = isDesktop
        ? 80.0
        : isTablet
            ? 70.0
            : 60.0;

    return Padding(
      padding: EdgeInsets.all(padding),
      child: Column(
        children: [
          SizedBox(height: topSpacing),
          // Profile image
          Container(
            width: imageSize,
            height: imageSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[100],
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipOval(
              child: _userData!['profile_image'] != null &&
                      _userData!['profile_image'].toString().isNotEmpty &&
                      _userData!['profile_image'] != 'null'
                  ? PlatformAwareNetworkImage(
                      imageUrl: _userData!['profile_image'].startsWith('http')
                          ? _userData!['profile_image']
                          : '${Constants.baseStorageUrl}${_userData!['profile_image']}',
                      width: imageSize,
                      height: imageSize,
                      fit: BoxFit.cover,
                      placeholder: (context, url) =>
                          const CircularProgressIndicator(
                        color: Colors.teal,
                      ),
                      errorWidget: (context, url, error) => Icon(
                        Icons.person,
                        size: iconSize,
                        color: Colors.grey[400],
                      ),
                    )
                  : Icon(
                      Icons.person,
                      size: iconSize,
                      color: Colors.grey[400],
                    ),
            ),
          ),
          SizedBox(height: nameSpacing),
          // Full name
          Text(
            fullName,
            style: TextStyle(
              fontSize: nameFontSize,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          // Username
          Text(
            username,
            style: TextStyle(
              fontSize: usernameFontSize,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: statsSpacing),
          // Statistics row
          _buildStatsRow(),
        ],
      ),
    );
  }

  Widget _buildStatsRow() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth >= 1024;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isDesktop
            ? 40.0
            : isTablet
                ? 30.0
                : 20.0,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatItem('360', 'Post'),
          _buildStatItem('160k', 'Follower'),
          _buildStatItem('140k', 'Following'),
        ],
      ),
    );
  }

  Widget _buildStatItem(String count, String label) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth >= 1024;
    final isTablet = screenWidth >= 768 && screenWidth < 1024;

    final countFontSize = isDesktop
        ? 32.0
        : isTablet
            ? 28.0
            : 24.0;
    final labelFontSize = isDesktop
        ? 20.0
        : isTablet
            ? 18.0
            : 16.0;
    final spacing = isDesktop
        ? 8.0
        : isTablet
            ? 6.0
            : 4.0;

    return Column(
      children: [
        Text(
          count,
          style: TextStyle(
            fontSize: countFontSize,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: spacing),
        Text(
          label,
          style: TextStyle(
            fontSize: labelFontSize,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildAppointmentsTab() {
    if (_isLoading) {
      return const Center(
          child: CircularProgressIndicator(color: AppColors.tealSurge));
    }

    return Column(
      children: [
        // Filter buttons
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              _buildFilterButton('upcoming', 'Upcoming'),
              const SizedBox(width: 8),
              _buildFilterButton('past', 'Past'),
              const SizedBox(width: 8),
              _buildFilterButton('all', 'All'),
            ],
          ),
        ),

        // Appointments list
        Expanded(
          child: _filteredAppointments.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.event_busy, size: 64, color: Colors.grey[300]),
                      const SizedBox(height: 16),
                      Text(
                        'No $_filter appointments',
                        style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadAppointments,
                  color: AppColors.tealSurge,
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredAppointments.length,
                    itemBuilder: (context, index) {
                      return AppointmentCard(
                        appointment: _filteredAppointments[index],
                        onActionCompleted: _loadAppointments,
                      );
                    },
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildFilterButton(String filterValue, String label) {
    final isSelected = _filter == filterValue;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _filter = filterValue;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.tealSurge : Colors.grey[200],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.black87,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSettings() {
    final isPatient = _userData!['role'] == 'patient';
    final isProvider = _userData!['role'] == 'provider';

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile image section
          Center(
            child: Stack(
              children: <Widget>[
                // Profile image
                CircleAvatar(
                  radius: 50,
                  backgroundColor: Colors.grey[100],
                  child: ClipOval(
                    child: _userData!['profile_image'] != null &&
                            _userData!['profile_image'].toString().isNotEmpty &&
                            _userData!['profile_image'] != 'null'
                        ? PlatformAwareNetworkImage(
                            imageUrl: _userData!['profile_image']
                                    .startsWith('http')
                                ? _userData!['profile_image']
                                : '${Constants.baseStorageUrl}${_userData!['profile_image']}',
                            width: 100,
                            height: 100,
                            fit: BoxFit.cover,
                            placeholder: (context, url) =>
                                const CircularProgressIndicator(
                                    color: AppColors.tealSurge),
                            errorWidget: (context, url, error) => Icon(
                              Icons.person,
                              size: 50,
                              color: Colors.grey[300],
                            ),
                          )
                        : Icon(
                            Icons.person,
                            size: 50,
                            color: Colors.grey[300],
                          ),
                  ),
                ),
                // Camera icon
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: _pickImage,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: const BoxDecoration(
                        color: AppColors.tealSurge,
                        shape: BoxShape.circle,
                      ),
                      child: _isUploadingImage
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(
                              Icons.camera_alt,
                              color: Colors.white,
                              size: 20,
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Personal Information Form
          Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Full Name',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Email (read-only)
                TextFormField(
                  initialValue: _userData!['email'] ?? '',
                  decoration: const InputDecoration(
                    labelText: 'Email',
                    border: OutlineInputBorder(),
                  ),
                  enabled: false,
                ),
                const SizedBox(height: 16),

                // Role badge
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: isPatient
                        ? Colors.blue.withAlpha(26)
                        : isProvider
                            ? Colors.green.withAlpha(26)
                            : Colors.grey.withAlpha(26),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isPatient
                        ? 'Patient'
                        : isProvider
                            ? 'Healthcare Provider'
                            : _userData!['role'] ?? 'User',
                    style: TextStyle(
                      color: isPatient
                          ? Colors.blue
                          : isProvider
                              ? Colors.green
                              : Colors.grey[700],
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
                const SizedBox(height: 24),

                // Save button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _saveProfile,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.tealSurge,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Save Changes'),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),
          const Divider(),
          const SizedBox(height: 16),

          // Account section
          const ProfileSectionHeader(title: 'Account'),
          ProfileOptionTile(
            icon: Icons.lock,
            title: 'Change Password',
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ChangePasswordScreen(),
                ),
              );
            },
          ),
          if (isPatient) ...[
            ProfileOptionTile(
              icon: Icons.favorite,
              title: 'Health Profile',
              onTap: () {
                // Navigate to health profile screen
              },
            ),
            ProfileOptionTile(
              icon: Icons.calendar_today,
              title: 'Appointment Preferences',
              onTap: () {
                _showAppointmentPreferencesDialog();
              },
            ),
          ],
          if (isProvider) ...[
            ProfileOptionTile(
              icon: Icons.business,
              title: 'Practice Information',
              onTap: () {
                Navigator.pushNamed(
                  context,
                  '/provider-dashboard',
                  arguments: {'initialTab': 3},
                );
              },
            ),
            ProfileOptionTile(
              icon: Icons.person_outline,
              title: 'Edit Provider Profile',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ProviderProfileEditScreen(),
                  ),
                ).then((value) {
                  if (value == true) {
                    _loadUserData();
                  }
                });
              },
            ),
          ],
          ProfileOptionTile(
            icon: Icons.notifications,
            title: 'Notification Settings',
            onTap: () {
              // Navigate to notification settings screen
            },
          ),

          // Debug section (only in debug mode)
          if (kDebugMode) ...[
            const SizedBox(height: 8),
            const ProfileSectionHeader(title: 'Debug'),
            ProfileOptionTile(
              icon: Icons.bug_report,
              title: 'Notification Debug',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const NotificationDebugScreen(),
                  ),
                );
              },
            ),
          ],

          // App section
          const SizedBox(height: 8),
          const ProfileSectionHeader(title: 'App'),
          ProfileOptionTile(
            icon: Icons.help,
            title: 'Help & Support',
            onTap: () {
              // Navigate to help screen
            },
          ),
          ProfileOptionTile(
            icon: Icons.privacy_tip,
            title: 'Privacy Policy',
            onTap: () {
              // Navigate to privacy policy screen
            },
          ),
          ProfileOptionTile(
            icon: Icons.description,
            title: 'Terms of Service',
            onTap: () {
              // Navigate to terms of service screen
            },
          ),

          // Logout button
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _logout,
              icon: const Icon(Icons.exit_to_app, size: 18),
              label: const Text('Logout'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade400,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
            ),
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = RepositoryProvider.of<AuthService>(context);

      final updateData = {
        'name': _nameController.text,
      };

      final success = await authService.updateProfile(updateData);

      if (!mounted) return;

      if (success) {
        await _loadUserData();

        if (!mounted) return;

        setState(() {
          _isEditing = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile updated successfully')),
        );
      } else {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to update profile')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating profile: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _showAppointmentPreferencesDialog() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final apiService = RepositoryProvider.of<ApiService>(context);
      final response = await apiService.get('/user/profile');

      setState(() {
        _isLoading = false;
      });

      final appointmentPreferences = response?['appointment_preferences'];

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Appointment Preferences'),
          content: SizedBox(
            width: 500,
            child: AppointmentPreferencesForm(
              initialPreferences: appointmentPreferences,
              onPreferencesSaved: (updatedPreferences) {
                Navigator.of(context).pop();

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Appointment preferences saved'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading preferences: $e')),
        );
      }
    }
  }
}
