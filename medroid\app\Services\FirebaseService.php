<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FirebaseService
{
    /**
     * Send a notification to a device token
     *
     * @param string $token
     * @param string $title
     * @param string $body
     * @param array $data
     * @return bool
     */
    public function sendNotification($token, $title, $body, $data = [])
    {
        try {
            Log::info('Firebase sendNotification called', [
                'token_length' => strlen($token),
                'title' => $title,
                'body' => $body,
                'data_keys' => array_keys($data),
            ]);

            // Try to use server key first, fallback to api key
            $serverKey = config('services.firebase.server_key');
            $apiKey = $serverKey ?: config('services.firebase.api_key');

            if (empty($apiKey)) {
                Log::error('Firebase server key is not configured');
                return false;
            }

            Log::info('Firebase server key found', [
                'key_length' => strlen($apiKey),
                'key_prefix' => substr($apiKey, 0, 10) . '...',
                'using_server_key' => !empty($serverKey),
            ]);

            // For Flutter, we use the legacy FCM API which works better with Flutter apps
            $payload = [
                'to' => $token,
                'notification' => [
                    'title' => $title,
                    'body' => $body,
                    'sound' => $data['type'] === 'video_call' ? 'call_ringtone.mp3' : 'default',
                    'badge' => '1',
                    'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                ],
                'data' => $data,
                'priority' => 'high',
                'content_available' => true,
                'mutable_content' => true,
            ];

            // Add Android specific configuration
            $payload['android'] = [
                'priority' => 'high',
                'notification' => [
                    'channel_id' => $data['type'] === 'video_call' ? 'video_call_channel' : 'high_importance_channel',
                    'sound' => $data['type'] === 'video_call' ? 'call_ringtone' : 'default',
                    'notification_priority' => 'PRIORITY_HIGH',
                    'visibility' => 'PUBLIC',
                    'category' => $data['type'] === 'video_call' ? 'call' : 'message',
                    'ongoing' => $data['type'] === 'video_call' ? true : false,
                    'auto_cancel' => $data['type'] === 'video_call' ? false : true,
                ]
            ];

            // Add iOS specific configuration
            $payload['apns'] = [
                'payload' => [
                    'aps' => [
                        'alert' => [
                            'title' => $title,
                            'body' => $body,
                        ],
                        'sound' => $data['type'] === 'video_call' ? 'call_ringtone.caf' : 'default',
                        'badge' => 1,
                        'category' => $data['type'] === 'video_call' ? 'INCOMING_CALL' : 'MESSAGE',
                        'content-available' => 1,
                        'mutable-content' => 1,
                    ]
                ]
            ];

            // Legacy FCM API endpoint
            $url = "https://fcm.googleapis.com/fcm/send";

            Log::info('Sending FCM request', [
                'url' => $url,
                'payload_keys' => array_keys($payload),
                'notification_title' => $payload['notification']['title'] ?? null,
                'token_length' => strlen($token),
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'key=' . $apiKey,
                'Content-Type' => 'application/json'
            ])->post($url, $payload);

            Log::info('FCM response received', [
                'status_code' => $response->status(),
                'response_body' => $response->body(),
                'is_successful' => $response->successful(),
                'token_length' => strlen($token),
            ]);

            if ($response->successful()) {
                Log::info('Push notification sent to Flutter app', [
                    'token' => $token,
                    'response' => $response->json()
                ]);
                return true;
            } else {
                $responseBody = $response->body();
                $statusCode = $response->status();

                // Check for specific error types
                $errorMessage = 'Failed to send push notification';
                if ($statusCode === 404) {
                    $errorMessage = 'Firebase server key is invalid or FCM service not found. Please check your Firebase server key configuration.';
                } elseif ($statusCode === 401) {
                    $errorMessage = 'Firebase server key is unauthorized. Please verify your Firebase server key.';
                } elseif (strpos($responseBody, 'InvalidRegistration') !== false) {
                    $errorMessage = 'Invalid device token. The token may be expired or invalid.';
                }

                Log::error($errorMessage, [
                    'token' => $token,
                    'response' => $responseBody,
                    'status_code' => $statusCode,
                    'headers' => $response->headers(),
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Error sending Firebase notification to Flutter app', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Send a notification to multiple device tokens
     *
     * @param array $tokens
     * @param string $title
     * @param string $body
     * @param array $data
     * @return bool
     */
    public function sendMulticastNotification(array $tokens, $title, $body, $data = [])
    {
        if (empty($tokens)) {
            return false;
        }

        $success = false;

        foreach ($tokens as $token) {
            $result = $this->sendNotification($token, $title, $body, $data);
            if ($result) {
                $success = true;
            }
        }

        return $success;
    }

    /**
     * Test the Firebase connection
     *
     * @return bool
     */
    public function testConnection()
    {
        try {
            // Try to use server key first, fallback to api key
            $serverKey = config('services.firebase.server_key');
            $apiKey = $serverKey ?: config('services.firebase.api_key');

            if (empty($apiKey)) {
                return false;
            }

            // Test connection by validating the API key with the legacy FCM API
            // This is the same API we use for sending notifications to Flutter
            $url = "https://fcm.googleapis.com/fcm/send";

            $response = Http::withHeaders([
                'Authorization' => 'key=' . $apiKey,
                'Content-Type' => 'application/json'
            ])->post($url, [
                'to' => 'test_token', // Invalid token, but API will still validate our key
                'notification' => [
                    'title' => 'Test',
                    'body' => 'Test',
                ],
                'content_available' => true,
                'priority' => 'high',
            ]);

            // Even with an invalid token, if our API key is valid, we'll get a specific error
            // rather than an authentication error
            $responseData = $response->json();

            // Check if we got a response with a "results" field, which indicates our key was accepted
            return $response->successful() ||
                   (isset($responseData['results']) && is_array($responseData['results']));
        } catch (\Exception $e) {
            Log::error('Error testing Firebase connection', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
