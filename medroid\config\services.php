<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mistral_nemo' => [
        'api_key' => env('MISTRAL_NEMO_API_KEY'),
        'api_url' => 'https://api.mistral.ai/v1/chat/completions',
        'model_name' => 'mistral-medium',
    ],

    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
        'organization' => env('OPENAI_ORGANIZATION'),
    ],

    'groq' => [
        'api_key' => env('GROQ_API_KEY'),
        'api_url' => 'https://api.groq.com/openai/v1/chat/completions',
        'model' => env('GROQ_MODEL', 'meta-llama/llama-4-scout-17b-16e-instruct'),
    ],

    'firebase' => [
        'api_key' => env('FIREBASE_API_KEY'),
        'server_key' => env('FIREBASE_SERVER_KEY'),
        'project_id' => env('FIREBASE_PROJECT_ID'),
    ],

    'stripe' => [
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
        'webhook' => [
            'secret' => env('STRIPE_WEBHOOK_SECRET'),
            'tolerance' => env('STRIPE_WEBHOOK_TOLERANCE', 300),
        ],
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT_URI'),
    ],

    'deepgram' => [
        'api_key' => env('DEEPGRAM_API_KEY'),
        'api_url' => 'https://api.deepgram.com/v1/listen',
        'model' => env('DEEPGRAM_MODEL', 'nova-2'),
        'language' => env('DEEPGRAM_LANGUAGE', 'en-US'),
    ],

    'medroid' => [
        'modes' => [
            'exam_mode' => env('MEDROID_ENABLE_EXAM_MODE', true),
            'general_health' => env('MEDROID_ENABLE_GENERAL_HEALTH', true),
            'transparent_reasoning' => env('MEDROID_ENABLE_TRANSPARENT_REASONING', true),
        ],
        'timeouts' => [
            'search' => env('MEDROID_SEARCH_TIMEOUT', 30),
            'reasoning' => env('MEDROID_REASONING_TIMEOUT', 45),
            'exam' => env('MEDROID_EXAM_TIMEOUT', 60),
        ],
        'default_mode' => env('MEDROID_DEFAULT_MODE', 'auto_detect'),
    ],

    'google_cloud' => [
        'project_id' => env('GOOGLE_CLOUD_PROJECT_ID'),
        'search_engine_id' => env('GOOGLE_SEARCH_ENGINE_ID'),
    ],

];
